using Microsoft.EntityFrameworkCore;

namespace HTC.Service.Data;

public class ZaloDbContext: DbContext
{
    public ZaloDbContext(DbContextOptions<ZaloDbContext> options) : base(options)
    {
    }
    
    public DbSet<ZaloAccount> ZaloAccounts { get; set; }
    public DbSet<ZaloNotificationSetting> ZaloNotificationSettings { get; set; }
    public DbSet<ZaloNotificationDataMapping> ZaloNotificationDataMappings { get; set; }
    public DbSet<ZaloNotification> ZaloNotifications { get; set; }
    public DbSet<ZaloBasicFlowResult> ZaloBasicFlowResults { get; set; }

    public void SeedData()
    {
        var zaloAccount = ZaloAccounts.OrderByDescending(x => x.Id).FirstOrDefault();
        if (zaloAccount == null)
        {
            zaloAccount = new ZaloAccount()
            {
                Name = "Hyundai Bắc Ninh",
                AppId = "3790050582187324468",
                OfficialAccountId = "3786896532662893166",
                IsEnabled = true,
            };

            ZaloAccounts.Add(zaloAccount);
            SaveChanges();
        }

        var paidRepairOrderNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == nameof(DMSDataType.PaidRepairOrder));
        if (paidRepairOrderNotification == null)
        {
            paidRepairOrderNotification = new ZaloNotificationSetting
            {
                Name = nameof(DMSDataType.PaidRepairOrder),
                DMSDataType = DMSDataType.PaidRepairOrder,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493219",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "order_code",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.No),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "license_plate",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.PlateNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "service_date",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.CheckInDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.Model),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer_name",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.CustomerName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    }
                }
            };
            
            ZaloNotificationSettings.Add(paidRepairOrderNotification);
            SaveChanges();
        }
        
        var paidRepairOrderCompanyNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == $"{nameof(DMSDataType.PaidRepairOrder)}-Company");
        if (paidRepairOrderCompanyNotification == null)
        {
            paidRepairOrderCompanyNotification = new ZaloNotificationSetting
            {
                Name = $"{nameof(DMSDataType.PaidRepairOrder)}-Company",
                DMSDataType = DMSDataType.PaidRepairOrder,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493693",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "order_code",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.No),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "license_plate",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.PlateNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "service_date",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.CheckInDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSPaidRepairOrderData.Detail.Model),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer_name",
                        ZaloFieldFormat = "Quý công ty",
                        DMSFields = "",
                        DMSFieldTypes = "",
                    }
                }
            };
            
            ZaloNotificationSettings.Add(paidRepairOrderCompanyNotification);
            SaveChanges();
        }
        
        var warrantyActivationNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == nameof(DMSDataType.WarrantyActivation));
        if (warrantyActivationNotification == null)
        {
            warrantyActivationNotification = new ZaloNotificationSetting
            {
                Name = nameof(DMSDataType.WarrantyActivation),
                DMSDataType = DMSDataType.WarrantyActivation,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493220",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.ModelName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer_name",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.CustomerName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "engine_number",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.EngineNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "vehicle_identification_number",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.VIN),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "warranty_active_date",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.WarrantyDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "warranty_expired_date",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.WarrantyExpiresDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "km_limit",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.WarrantyKM),
                        DMSFieldTypes = $"{DMSFieldType.Double}",
                    },  
                }
            };
            
            ZaloNotificationSettings.Add(warrantyActivationNotification);
            SaveChanges();
        }
        
        var warrantyActivationCompanyNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == $"{nameof(DMSDataType.WarrantyActivation)}-Company");
        if (warrantyActivationCompanyNotification == null)
        {
            warrantyActivationCompanyNotification = new ZaloNotificationSetting
            {
                Name = $"{nameof(DMSDataType.WarrantyActivation)}-Company",
                DMSDataType = DMSDataType.WarrantyActivation,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493692",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.ModelName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer_name",
                        ZaloFieldFormat = "Quý công ty",
                        DMSFields = "",
                        DMSFieldTypes = "",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "engine_number",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.EngineNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "vehicle_identification_number",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.VIN),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "warranty_active_date",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.WarrantyDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "warranty_expired_date",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.WarrantyExpiresDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "km_limit",
                        DMSFields = nameof(DMSWarrantyActivationData.Detail.WarrantyKM),
                        DMSFieldTypes = $"{DMSFieldType.Double}",
                    },  
                }
            };
            
            ZaloNotificationSettings.Add(warrantyActivationCompanyNotification);
            SaveChanges();
        }
        
        var maintenanceReminderNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == nameof(DMSDataType.MaintenanceReminder));
        if (maintenanceReminderNotification == null)
        {
            maintenanceReminderNotification = new ZaloNotificationSetting
            {
                Name = nameof(DMSDataType.MaintenanceReminder),
                DMSDataType = DMSDataType.MaintenanceReminder,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493221",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSMaintenanceReminderData.Detail.ModelName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "license_plate",
                        DMSFields = nameof(DMSMaintenanceReminderData.Detail.PlateNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer_name",
                        DMSFields = nameof(DMSMaintenanceReminderData.Detail.CustomerName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                }
            };
            
            ZaloNotificationSettings.Add(maintenanceReminderNotification);
            SaveChanges();
        }
        
        var maintenanceReminderCompanyNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == $"{nameof(DMSDataType.MaintenanceReminder)}-Company");
        if (maintenanceReminderCompanyNotification == null)
        {
            maintenanceReminderCompanyNotification = new ZaloNotificationSetting
            {
                Name = $"{nameof(DMSDataType.MaintenanceReminder)}-Company",
                DMSDataType = DMSDataType.MaintenanceReminder,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493691",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSMaintenanceReminderData.Detail.ModelName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "license_plate",
                        DMSFields = nameof(DMSMaintenanceReminderData.Detail.PlateNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer_name",
                        ZaloFieldFormat = "Quý công ty",
                        DMSFields = "",
                        DMSFieldTypes = "",
                    }
                }
            };
            
            ZaloNotificationSettings.Add(maintenanceReminderCompanyNotification);
            SaveChanges();
        }
        
        var customerBirthdayNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == nameof(DMSDataType.CustomerBirthday));
        if (customerBirthdayNotification == null)
        {
            customerBirthdayNotification = new ZaloNotificationSetting
            {
                Name = nameof(DMSDataType.CustomerBirthday),
                DMSDataType = DMSDataType.CustomerBirthday,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "494559",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "voucher_code",
                        DMSFields = nameof(DMSCustomerBirthdayData.Detail.VoucherCode),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "expire_date",
                        DMSFields = nameof(DMSCustomerBirthdayData.Detail.VoucherExpiredDate),
                        DMSFieldTypes = $"{DMSFieldType.Date}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "phone_number",
                        DMSFields = nameof(DMSCustomerBirthdayData.Detail.PhoneNumber),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer",
                        DMSFields = nameof(DMSCustomerBirthdayData.Detail.CustomerName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "amount",
                        DMSFields = nameof(DMSCustomerBirthdayData.Detail.VoucherAmount),
                        DMSFieldTypes = $"{DMSFieldType.Decimal}",
                    },
                }
            };
            
            ZaloNotificationSettings.Add(customerBirthdayNotification);
            SaveChanges();
        }
        
        var appointmentNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == nameof(DMSDataType.Appointment));
        if (appointmentNotification == null)
        {
            appointmentNotification = new ZaloNotificationSetting
            {
                Name = nameof(DMSDataType.Appointment),
                DMSDataType = DMSDataType.Appointment,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493223",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSAppointmentData.Detail.TrademarkModel),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "license_plate",
                        DMSFields = nameof(DMSAppointmentData.Detail.PlateNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "time",
                        DMSFields = nameof(DMSAppointmentData.Detail.AppointmentFromDate),
                        DMSFieldTypes = $"{DMSFieldType.DateTime}",
                        ZaloFieldFormat = "{0:d:HH:mm dd/MM/yyyy}"
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "appointment_code",
                        DMSFields = nameof(DMSAppointmentData.Detail.AppointmentNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer",
                        DMSFields = nameof(DMSAppointmentData.Detail.CustomerName),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                }
            };
            
            ZaloNotificationSettings.Add(appointmentNotification);
            SaveChanges();
        }
        
        var appointmentCompanyNotification =
            ZaloNotificationSettings.OrderByDescending(x => x.Id).FirstOrDefault(x => x.Name == $"{nameof(DMSDataType.Appointment)}-Company");
        if (appointmentCompanyNotification == null)
        {
            appointmentCompanyNotification = new ZaloNotificationSetting
            {
                Name = $"{nameof(DMSDataType.Appointment)}-Company",
                DMSDataType = DMSDataType.Appointment,
                ZaloAccount = zaloAccount,
                IsEnabled = true,
                ZaloTemplateId = "493690",
                ZaloNotificationDataMappings = new List<ZaloNotificationDataMapping>()
                {
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "model",
                        DMSFields = nameof(DMSAppointmentData.Detail.TrademarkModel),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "license_plate",
                        DMSFields = nameof(DMSAppointmentData.Detail.PlateNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "time",
                        DMSFields = nameof(DMSAppointmentData.Detail.AppointmentFromDate),
                        DMSFieldTypes = $"{DMSFieldType.DateTime}",
                        ZaloFieldFormat = "{0:d:HH:mm dd/MM/yyyy}"
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "appointment_code",
                        DMSFields = nameof(DMSAppointmentData.Detail.AppointmentNo),
                        DMSFieldTypes = $"{DMSFieldType.String}",
                    },
                    new ZaloNotificationDataMapping()
                    {
                        ZaloField = "customer",
                        ZaloFieldFormat = "Quý công ty",
                        DMSFields = "",
                        DMSFieldTypes = "",
                    }
                }
            };
            
            ZaloNotificationSettings.Add(appointmentCompanyNotification);
            SaveChanges();
        }
    }
}