using HTC.Service.Data;
using HTC.Zalo;
using HTC.Zalo.Clients.Notification;
using HTC.Zalo.Request.Notification;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;

namespace HTC.Api.Controllers;

[ApiController]
[Route("api/[controller]/[action]")]
public class ZaloBasicFlowController : ControllerBase
{
    private readonly ILogger<ZaloBasicFlowController> _logger;
    private readonly IConfiguration _configuration;
    private readonly ZaloDbContext _zaloDbContext;

    public ZaloBasicFlowController(ILogger<ZaloBasicFlowController> logger, IConfiguration configuration, ZaloDbContext zaloDbContext)
    {
        _logger = logger;
        _configuration = configuration;
        _zaloDbContext = zaloDbContext;
    }

    [HttpPost]
    public async Task<ActionResult<ExcelZaloBasicFlowResponse>> ExcelZaloBasicFlow([FromForm] ExcelZaloBasicFlowRequest request)
    {
        if (request.File == null || request.File.Length == 0)
            return BadRequest("Excel file is required");

        var response = new ExcelZaloBasicFlowResponse();
        var results = new List<ZaloBasicFlowResult>();

        try
        {
            // Get Zalo account settings
            var zaloAccount = await _zaloDbContext.ZaloAccounts
                .Where(x => x.IsEnabled)
                .FirstOrDefaultAsync();

            if (zaloAccount == null)
                return BadRequest("No enabled Zalo account found");

            // Parse Excel file
            var excelData = await ParseExcelFile(request.File);
            response.TotalProcessed = excelData.Count;

            // Create Zalo client
            using var zaloClient = new ZaloNotificationClient(new ZaloSDKOptions()
                .UseZNSAPI()
                .WithApp(zaloAccount.AppId, zaloAccount.AppSecret)
                .WithAccessToken(zaloAccount.AccessToken)
                .WithRefreshToken(zaloAccount.RefreshToken));

            var isDevelopmentMode = _configuration.GetValue<bool>("IsZaloDevelopmentMode");
            var developerPhone = _configuration.GetValue<string>("ZaloDeveloperPhone")!;

            // Process each row
            foreach (var row in excelData)
            {
                var result = new ZaloBasicFlowResult
                {
                    Phone = row.Phone,
                    FullName = row.FullName,
                    TemplateId = request.TemplateId,
                    TimeSent = DateTime.Now
                };

                try
                {
                    var templateData = new Dictionary<string, string>
                    {
                        ["customer_name"] = row.FullName
                    };

                    var zaloResponse = await zaloClient.SendNotification(new SendNotificationRequest
                    {
                        IsDevelopmentMode = isDevelopmentMode,
                        Phone = isDevelopmentMode ? developerPhone : row.Phone,
                        TemplateId = request.TemplateId.ToString(),
                        TrackingId = $"BasicFlow-{row.Phone}-{DateTime.Now:yyyyMMddHHmmss}",
                        TemplateData = templateData
                    });

                    if (zaloResponse.IsSuccess)
                    {
                        result.Status = "Success";
                        result.ZaloMessageId = zaloResponse.Data?.MsgId;
                        response.SuccessfulSends++;
                    }
                    else
                    {
                        result.Status = zaloResponse.ErrorMessage ?? "Unknown error";
                        response.FailedSends++;
                    }
                }
                catch (Exception ex)
                {
                    result.Status = ex.Message;
                    response.FailedSends++;
                    _logger.LogError(ex, "Failed to send Zalo notification to {Phone}", row.Phone);
                }

                results.Add(result);
            }

            // Save results to database
            await _zaloDbContext.ZaloBasicFlowResults.AddRangeAsync(results);
            await _zaloDbContext.SaveChangesAsync();

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process Excel Zalo Basic Flow");
            return StatusCode(500, "Internal server error");
        }
    }

    private async Task<List<ExcelRowData>> ParseExcelFile(IFormFile file)
    {
        var results = new List<ExcelRowData>();
        
        using var stream = new MemoryStream();
        await file.CopyToAsync(stream);
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        using var package = new ExcelPackage(stream);
        var worksheet = package.Workbook.Worksheets[0];
        
        for (int row = 2; row <= worksheet.Dimension.Rows; row++)
        {
            var phone = worksheet.Cells[row, 1].Text?.Trim();
            var fullName = worksheet.Cells[row, 2].Text?.Trim();
            
            if (!string.IsNullOrEmpty(phone) && !string.IsNullOrEmpty(fullName))
            {
                results.Add(new ExcelRowData
                {
                    Phone = phone,
                    FullName = fullName
                });
            }
        }
        
        return results;
    }
}

public class ExcelZaloBasicFlowRequest
{
    public int TemplateId { get; set; }
    public IFormFile File { get; set; } = null!;
}

public class ExcelZaloBasicFlowResponse
{
    public int TotalProcessed { get; set; }
    public int SuccessfulSends { get; set; }
    public int FailedSends { get; set; }
}

public class ExcelRowData
{
    public string Phone { get; set; } = null!;
    public string FullName { get; set; } = null!;
}